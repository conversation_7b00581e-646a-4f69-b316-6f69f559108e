pipeline {
  agent any

  environment {
    AWS_REGION = 'us-east-1'
    AWS_ACCOUNT_ID = '************'
    ECR_REPO = 'beyondself-auth-microservice'
    EKS_CLUSTER = 'beyond-self-eks-cluster'
    ACM_CERT_ARN = 'arn:aws:acm:us-east-1:************:certificate/6219fa2f-28d8-42e9-9484-ceb89ac7581d'
    IMAGE_TAG = "${env.BUILD_NUMBER}"
    FULL_IMAGE = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO}:${IMAGE_TAG}"
  }

  stages {

    stage('Checkout') {
      steps {
        checkout scm
      }
    }

    stage('Build Java App') {
      steps {
        echo "Building Java App with Maven"
        sh 'mvn clean package -DskipTests'
      }
    }

    stage('Build Docker Image') {
      steps {
        script {
          sh """
            aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
            docker build -t $ECR_REPO:$IMAGE_TAG .
            docker tag $ECR_REPO:$IMAGE_TAG $FULL_IMAGE
          """
        }
      }
    }

    stage('Push to ECR') {
      steps {
        sh "docker push $FULL_IMAGE"
      }
    }

    stage('Deploy to EKS') {
      steps {
        script {
          sh '''
            # Update kubeconfig for EKS
            aws eks update-kubeconfig --region $AWS_REGION --name $EKS_CLUSTER

            # Replace image and cert placeholders
            sed "s|<FULL_IMAGE>|$FULL_IMAGE|g" k8s/deployment.yaml > k8s/deployment.rendered.yaml
            sed "s|<ACM_CERT_ARN>|$ACM_CERT_ARN|g" k8s/ingress.yaml > k8s/ingress.rendered.yaml

            # Apply all Kubernetes manifests
            kubectl apply -f k8s/deployment.rendered.yaml
            kubectl apply -f k8s/service.yaml
            kubectl apply -f k8s/ingress.rendered.yaml

            # Wait for the deployment to roll out
            kubectl rollout status deployment auth-service
          '''
        }
      }
    }
  }
}
