pipeline {
  agent any

  environment {
    AWS_REGION = 'us-east-1'
    AWS_ACCOUNT_ID = '************'
    ECR_REPO = 'beyondself-auth-microservice'
    IMAGE_TAG = "${env.BUILD_NUMBER}"
    FULL_IMAGE = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO}:${IMAGE_TAG}"
  }

  stages {
    stage('Checkout') {
      steps {
        checkout scm
      }
    }

  stage('Build Java App') {
    steps {
      echo "Building Java App with Maven"
      sh 'mvn clean package'
      }
    }
    
    stage('Build Docker Image') {
      steps {
        script {
          sh """
            aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
            docker build -t $ECR_REPO:$IMAGE_TAG .
            docker tag $ECR_REPO:$IMAGE_TAG $FULL_IMAGE
          """
        }
      }
    }

    stage('Push to ECR') {
      steps {
        sh "docker push $FULL_IMAGE"
      }
    }
  }
}
