# Auth-Service

## Standalone Authentication Service

This is a standalone authentication microservice that provides comprehensive user authentication and management capabilities. The service can run independently without requiring service discovery or API gateway components.

---

## Prerequisites

Ensure you have the following installed on your system:
- **Git**: To clone the repository
- **Docker**: To build and run containers (optional)
- **Java 21**
- **<PERSON><PERSON>**
- **MySQL**: Database for user data storage

---

## Quick Start

### Option 1: Run with <PERSON><PERSON> (Development)

1. Clone the repository:
```bash
git clone https://github.com/mosesmuriiki2/BeyondSelfAuthMicroService.git
cd BeyondSelfAuthMicroService
```

2. Configure your database in `src/main/resources/application.yml`

3. Run the application:
```bash
mvn spring-boot:run
```

The service will start on `http://localhost:8090`

### Option 2: Run with Docker

1. Clone the repository:
```bash
git clone https://github.com/mosesmuriiki2/BeyondSelfAuthMicroService.git
cd BeyondSelfAuthMicroService
```

2. Build the Docker image:
```bash
docker build -t auth-service .
```

3. Run the container:
```bash
docker run -d --name auth-service -p 8090:8090 auth-service
```

## API Endpoints

The service provides the following main endpoints:

### Public Endpoints (No Authentication Required)
- **POST** `/api/v1/user/register` - User registration
- **POST** `/api/v1/user/login` - User login
- **PUT** `/api/v1/user/validate-email-verification-token` - Email verification
- **PUT** `/api/v1/user/send-password-reset-token` - Request password reset
- **PUT** `/api/v1/user/reset-password` - Reset password
- **POST** `/api/v1/user/refresh-token` - Refresh JWT token

### Protected Endpoints (Authentication Required)
- **GET** `/api/v1/user/send-email-verification-token` - Send email verification
- **POST** `/api/v1/user/{id}/uploadProfilePicture` - Upload profile picture
- **GET** `/api/v1/user/{id}/profilePicture` - Get profile picture
- **DELETE** `/api/v1/user/{id}/profilePicture` - Delete profile picture

### Admin Endpoints (Admin Role Required)
- **GET** `/api/v1/users` - Get all active users
- **GET** `/api/v1/users/trashed` - Get trashed users
- **PUT** `/api/v1/users/{id}/trash` - Move user to trash
- **PUT** `/api/v1/users/{id}/restore` - Restore user from trash

## Testing the Service

### User Registration
```bash
curl -X POST http://localhost:8090/api/v1/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123",
    "email": "<EMAIL>",
    "firstName": "Test",
    "lastName": "User",
    "gender": "Male",
    "dateOfBirth": "1990-01-01",
    "phoneNumber": "+1234567890"
  }'
```

### User Login
```bash
curl -X POST http://localhost:8090/api/v1/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "userName": "testuser",
    "password": "password123"
  }'
```

## API Documentation

### Swagger UI
Access the interactive API documentation at:
- **Swagger UI**: http://localhost:8090/api/v1/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8090/api/v1/v3/api-docs

The Swagger UI provides:
- Complete API documentation
- Interactive endpoint testing
- Request/response schemas
- Authentication testing with JWT tokens

### Features

- **JWT Authentication**: Stateless authentication using JSON Web Tokens
- **Email Verification**: Secure email verification system
- **Password Reset**: Token-based password reset functionality
- **Profile Management**: User profile picture upload and management
- **Role-based Access Control**: Admin and user roles with specific permissions
- **Input Validation**: Comprehensive request validation
- **Error Handling**: Standardized error responses
- **Database Integration**: MySQL database with JPA/Hibernate

### Configuration

Update the following configuration in `application.yml`:

```yaml
spring:
  datasource:
    url: ***********************************************
    username: your-username
    password: your-password

  mail:
    host: your-smtp-host
    port: 587
    username: your-email
    password: your-email-password
```

### Security

- Passwords are encrypted using BCrypt
- JWT tokens are signed using RSA keys
- Email verification and password reset tokens have expiration times
- CORS is configured for cross-origin requests
- Input validation prevents malicious data