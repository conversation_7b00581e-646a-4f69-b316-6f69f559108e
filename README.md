# Auth-Service

## Microservices Setup Guide

This guide provides step-by-step instructions to set up and run the following microservices:
1. **API Gateway Service**
2. **Discovery Server**
3. **Auth Service**

Each service resides in its own GitHub repository and has its own Dockerfile for containerization.

---

## Prerequisites

Ensure you have the following installed on your system:
- **Git**: To clone repositories
- **Docker**: To build and run containers
- **Java 21**
- **Maven**

---

## Step 1: Clone the Repositories

### Clone Each Repository

1. Open your terminal.
2. Clone the repositories using the commands below:

```bash

   git clone [Api-Gateway](https://github.com/mosesmuriiki2/BeyondSelfApiGatewayService) apigateway
   git clone [Discovery-Service](https://github.com/mosesmuriiki2/BeyondSelfDiscoveryService) discovery-server
   git clone [Auth-Server](https://github.com/mosesmuriiki2/BeyondSelfAuthMicroService) auth-server

```

## Step 2: Create a docker bridge network

```bash

docker network create --driver bridge beyond-self

```
## Build the Docker images for the services.

### Build the different service images
```bash 
# Build the discovery-service image
cd discovery-server
docker build -t discovery-server .

# Build the Apigateway image
cd ../api-gateway
docker build -t api-gateway .

# Build Auth Server Image
cd ../auth-server
docker build -t auth-server .

```

## Step 3: Run Docker containers for the different services.

```bash
# Start Discovery Server (must start first)
docker run -d --name discovery-server --network "beyond-self" -p 8091:8091 discovery-server

# Start API Gateway
docker run -d --name api-gateway --network "beyond-self" -p 8092:8092 api-gateway

# Start Auth Server
docker run -d --name auth-server --network "beyond-self" -p 8090:8090 auth-server
```

## Step 4: Verify the Services.
- Discovery Server: Open a browser and navigate to http://localhost:8091. You should see the Eureka dashboard. Ensure you have have the **api gateway** and **auth-service** recorded as registered in the Eureka server.

- API Gateway and Auth Server: Test by sending requests to the API Gateway endpoint (http://localhost:8092).

- User Registration: Test user registration at the endpoint http://localhost:8092/api/v1/user/register.

- User login: Test registered user login at the endpoint http://localhost:8092/api/v1/user/login

## Swagger
- For Swagger UI visit http://localhost:8090/api/v1/swagger-ui.html
- For OpenAPI JSON visit http://localhost:8090/api/v1/v3/api-docs