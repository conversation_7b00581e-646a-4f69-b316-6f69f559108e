spring:
    application:
        name: authservice
    datasource:
        url: **********************************************************************************************
        username: admin
        password: pnVSYhWAQ3>2Ug)Tl-W(0y6b7b8.
        driver-class-name: com.mysql.cj.jdbc.Driver
        hikari:
            connection-timeout: 20000
            minimum-idle: 5
            maximum-pool-size: 10
            idle-timeout: 30000
            max-lifetime: 1800000
    jpa:
        hibernate:
            ddl-auto: update
        properties:
            hibernate:
                dialect: org.hibernate.dialect.MySQL8Dialect

    mail:
        host: mail.cogni-x.com
        port: 465
        username: <EMAIL>
        password: admin@cogniX14%P97
        properties:
            mail:
                smtp:
                    auth: true
                    ssl.enable: true
                    starttls.enable: false

eureka:
    client:
        register-with-eureka: true
        fetch-registry: true
        registry-fetch-interval-seconds: 5   # Period between fetches
        initial-instance-info-replication-interval-seconds: 5
        service-url:
            defaultZone: http://localhost:8091/eureka
            # http://localhost:8091/eureka
            
    instance:
        preferIpAddress: true
    logging:
        level:
            com.netflix.discovery: DEBUG


keyStore:
    path: keystore.jks
    password: password

server:
    port: 8090
    servlet:
        context-path: /api/v1

application:
    security:
        jwt:
            refresh-token:
                expiration: 604800000 #7 days

logging:
    level:
        org:
            springframework:
                security: DEBUG

springdoc:
    api-docs:
        enabled: true
    swagger-ui:
        path: /swagger-ui.html
        operations-sorter: method


management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
  health:
    defaults:
      enabled: true
