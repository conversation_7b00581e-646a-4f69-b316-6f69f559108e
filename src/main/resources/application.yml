spring:
    application:
        name: authservice
    datasource:
        url: ******************************************
        username: root
        password: Password@224
        driver-class-name: com.mysql.cj.jdbc.Driver
        hikari:
            connection-timeout: 20000
            minimum-idle: 5
            maximum-pool-size: 10
            idle-timeout: 30000
            max-lifetime: 1800000
    jpa:
        hibernate:
            ddl-auto: update
        properties:
            hibernate:
                dialect: org.hibernate.dialect.MySQL8Dialect

    mail:
        host: mail.cogni-x.com
        port: 465
        username: <EMAIL>
        password: admin@cogniX14%P97
        properties:
            mail:
                smtp:
                    auth: true
                    ssl.enable: true
                    starttls.enable: false




keyStore:
    path: keystore.jks
    password: password

server:
    port: 8090
    servlet:
        context-path: /api/v1

application:
    security:
        jwt:
            refresh-token:
                expiration: 604800000 #7 days

logging:
    level:
        org:
            springframework:
                security: DEBUG

springdoc:
    api-docs:
        enabled: true
    swagger-ui:
        path: /swagger-ui.html
        operations-sorter: method


management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
  health:
    defaults:
      enabled: true
