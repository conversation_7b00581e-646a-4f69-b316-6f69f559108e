package com.cognix.authservice.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.Components;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Value("${server.servlet.context-path:/api/v1}")
    private String contextPath;

    @Value("${server.port:8090}")
    private String serverPort;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Development Server"),
                        new Server()
                                .url("https://auth-service.cogni-x.com" + contextPath)
                                .description("Production Server")
                ))
                .components(new Components()
                        .addSecuritySchemes("bearerAuth", createBearerAuthScheme())
                        .addSecuritySchemes("refreshToken", createRefreshTokenScheme())
                )
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"));
    }

    private Info apiInfo() {
        return new Info()
                .title("BeyondSelf Authentication Service API")
                .description("""
                        ## Standalone Authentication Service API Documentation

                        This is a standalone authentication microservice that provides comprehensive user authentication
                        and management capabilities. The service runs independently without requiring service discovery
                        or API gateway components.

                        ### Features:
                        - **User Registration & Login**: Secure user registration with email verification
                        - **JWT Authentication**: Stateless authentication using JWT tokens
                        - **Password Management**: Password reset functionality with secure tokens
                        - **Profile Management**: User profile picture upload and management
                        - **Admin Operations**: User management operations for administrators
                        - **Email Verification**: Email verification system for new users
                        - **Token Refresh**: Secure token refresh mechanism
                        - **Standalone Operation**: No dependencies on service discovery or API gateway

                        ### Security:
                        - All endpoints (except public ones) require JWT authentication
                        - Admin endpoints require ADMIN role and specific permissions
                        - Passwords are encrypted using BCrypt
                        - Email verification tokens and password reset tokens have expiration times
                        - RSA-based JWT signing for enhanced security

                        ### Getting Started:
                        1. Register a new user using `/user/register`
                        2. Verify your email using the token sent to your email
                        3. Login using `/user/login` to get your JWT token
                        4. Use the JWT token in the Authorization header for protected endpoints

                        ### Authentication:
                        Use the **Authorize** button above to add your JWT token for testing protected endpoints.
                        Format: `Bearer your-jwt-token-here`
                        """)
                .version("1.0.0")
                .contact(new Contact()
                        .name("BeyondSelf Development Team")
                        .email("<EMAIL>")
                        .url("https://github.com/mosesmuriiki2/BeyondSelfAuthMicroService"))
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT"));
    }

    private SecurityScheme createBearerAuthScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .description("Enter your JWT token obtained from the login endpoint");
    }

    private SecurityScheme createRefreshTokenScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.APIKEY)
                .in(SecurityScheme.In.COOKIE)
                .name("refreshToken")
                .description("Refresh token stored in HTTP-only cookie");
    }
}
