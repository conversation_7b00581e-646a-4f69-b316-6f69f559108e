package com.cognix.authservice.model;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.security.core.authority.SimpleGrantedAuthority;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
public enum RoleName {
    
ROLE_USER(
        Set.of(
            Permission.USER_READ,
            Permission.USER_CREATE,
            Permission.USER_DELETE,
            Permission.USER_UPDATE
        )
    ),

    ROLE_ADMIN(
        Set.of(
            Permission.ADMIN_READ,
            Permission.ADMIN_CREATE,
            Permission.ADMIN_DELETE,
            Permission.ADMIN_UPDATE,

            Permission.THERAPIST_READ,
            Permission.THERAPIST_CREATE,
            Permission.THERAPIST_DELETE,
            Permission.THERAPIST_UPDATE,

            Permission.USER_READ,
            Permission.USER_CREATE,
            Permission.USER_DELETE,
            Permission.USER_UPDATE
        )
    ),

    ROLE_THERAPIST(
        Set.of(
            Permission.THERAPIST_READ,
            Permission.THERAPIST_CREATE,
            Permission.THERAPIST_DELETE,
            Permission.THERAPIST_UPDATE
        )
    )

    ;

    @Getter
    private final Set<Permission> permissions;

    public List<SimpleGrantedAuthority> getAuthorities(){

      var authorities =  getPermissions()
                            .stream()
                            .map(permission -> new SimpleGrantedAuthority(permission.getPermission()))
                            .collect(Collectors.toList());
        
         authorities.add(new SimpleGrantedAuthority(this.name()));
         log.info(this.name());
         System.out.println(this.name());

            return authorities;
    }
}
