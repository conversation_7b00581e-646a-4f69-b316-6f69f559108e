package com.cognix.authservice.model;


import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name="users")
@NoArgsConstructor
@AllArgsConstructor

public class UserEntity {

    @Id
    @GeneratedValue(strategy  = GenerationType.IDENTITY)
    private long Id;

    @Column( unique = true, nullable = false)
    private String username;

    @Column(nullable = false)
    private String password;

    private String passwordResetToken = null;

    private LocalDateTime passwordResetTokenExpiryDate = null;

    @Column(nullable = false, unique = true)
    private String email;

    private Boolean emailVerified = false;

    private String emailVerificationToken = null;

    private LocalDateTime emailVerificationTokenExpiryDate = null;

    @Column(nullable = false)

    private String firstName;

    @Column(nullable = false)
    private String lastName;

    @Column(nullable = false)
    private String gender;

    @Column(nullable = false)
    
    private String dateOfBirth;

    @Column(nullable = false)    
    private String phoneNumber;

    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] profilePicture;

    private String accountStatus;

    private boolean isTrashed = false;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "role_id", nullable = false)
    private RoleEntity role;
}
