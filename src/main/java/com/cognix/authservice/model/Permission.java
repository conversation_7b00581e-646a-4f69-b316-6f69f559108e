package com.cognix.authservice.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum Permission {
    
    ADMIN_READ("admin:read"),

    ADMIN_UPDATE("admin:update"),

    ADMIN_CREATE("admin:create"),

    ADMIN_DELETE("admin:delete"),


    THERAPIST_READ("therapist:read"),

    THERAPIST_UPDATE("therapist:update"),

    THERAPIST_CREATE("therapist:create"),

    THERAPIST_DELETE("therapist:delete"),


    USER_READ("user:read"),

    USER_UPDATE("user:update"),

    USER_CREATE("user:create"),

    USER_DELETE("user:delete");



    @Getter
    private final String permission;
}
