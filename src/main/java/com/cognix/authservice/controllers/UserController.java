package com.cognix.authservice.controllers;

import java.io.UnsupportedEncodingException;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.cognix.authservice.Dtos.PasswordResetDto;
import com.cognix.authservice.Dtos.ResetPasswordReqDto;
import com.cognix.authservice.Dtos.ValidateEmailDto;
import com.cognix.authservice.model.UserEntity;
import com.cognix.authservice.request.SignUpRequest;
import com.cognix.authservice.services.UserDto;
import com.cognix.authservice.services.UserServiceImpl;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
import jakarta.mail.MessagingException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import java.io.IOException;

@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

   private final UserServiceImpl userServiceImpl;


   @PostMapping("/register")
   public ResponseEntity<?> registerUser(@RequestBody @Valid SignUpRequest signUpRequest) throws MessagingException, UnsupportedEncodingException{

    return new ResponseEntity<>(userServiceImpl.registerUser(signUpRequest), HttpStatus.CREATED);
   }

   @PostMapping("/login")
   public ResponseEntity<?> loginUser(@RequestBody UserDto user ){

    return new ResponseEntity<>(userServiceImpl.login(user.getUserName(), user.getPassword()), HttpStatus.OK);
   }

   @PutMapping("/validate-email-verification-token")
    public ResponseEntity<?> verifyEmail(@RequestBody ValidateEmailDto validateEmailDto) {
        userServiceImpl.validateEmailVerificationToken(validateEmailDto.getToken(), validateEmailDto.getEmail());
        return new ResponseEntity<>("Email verified successfully.", HttpStatus.OK);
    }

    @GetMapping("/send-email-verification-token")
    public ResponseEntity<?> sendEmailVerificationToken(@RequestAttribute("authenticatedUser") UserEntity user)  {
        userServiceImpl.sendEmailVerificationToken(user.getUsername());
        return new ResponseEntity<>("Email verification token sent successfully.", HttpStatus.OK);
    }

    @PutMapping("/send-password-reset-token")
    public ResponseEntity<?> sendPasswordResetToken(@RequestBody ResetPasswordReqDto resetPasswordReqDto) {
        userServiceImpl.sendPasswordResetToken(resetPasswordReqDto.getEmail());
        return new ResponseEntity<>("Password reset token sent successfully." , HttpStatus.OK);
    }

    @PutMapping("/reset-password")
    public ResponseEntity<?> resetPassword(@RequestBody PasswordResetDto passwordResetDto) {
        userServiceImpl.resetPassword(passwordResetDto.getEmail(), passwordResetDto.getNewPassword(), passwordResetDto.getToken());
        return new ResponseEntity<>("Password reset successfully." , HttpStatus.OK);
    }

     @PostMapping("/refresh-token")
    public void refreshToken(
            HttpServletRequest request,
            HttpServletResponse response
        ) throws SignatureException, ExpiredJwtException, UnsupportedJwtException, MalformedJwtException, IllegalArgumentException, AuthenticationException, Exception {
            userServiceImpl.refreshToken(request, response);
    }

    @PostMapping("/{id}/uploadProfilePicture")
    public ResponseEntity<String> uploadProfilePicture(@PathVariable Long id,
                                                       @RequestParam("file") MultipartFile file) {
        try {
            userServiceImpl.updateProfilePicture(id, file);
            return ResponseEntity.ok("Profile picture updated successfully.");
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to upload profile picture: " + e.getMessage());
        }
    }

    @GetMapping("/{id}/profilePicture")
    public ResponseEntity<byte[]> getProfilePicture(@PathVariable Long id) {
        byte[] image = userServiceImpl.getProfilePicture(id);
        if (image == null) {
            return ResponseEntity.notFound().build();
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_JPEG); 
        return new ResponseEntity<>(image, headers, HttpStatus.OK);
    }

    @DeleteMapping("/{id}/profilePicture")
    public ResponseEntity<String> deleteProfilePicture(@PathVariable Long id) {
    boolean deleted = userServiceImpl.deleteProfilePicture(id);
    if (deleted) {
        return ResponseEntity.ok("Profile picture deleted successfully.");
    } else {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Profile picture not found.");
    }
}

    
}
