package com.cognix.authservice.controllers;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cognix.authservice.model.UserEntity;
import com.cognix.authservice.services.UserServiceImpl;

import lombok.RequiredArgsConstructor;

import java.util.List;

@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
public class AdminController {
    

    private final UserServiceImpl userServiceImpl;

    @GetMapping
    public ResponseEntity<List<UserEntity>> getActiveUsers() {
        return ResponseEntity.ok(userServiceImpl.getAllActiveUsers());
    }

    @GetMapping("/trashed")
    public ResponseEntity<List<UserEntity>> getTrashedUsers() {
        return ResponseEntity.ok(userServiceImpl.getTrashedUsers());
    }

    @PutMapping("/{id}/trash")
    public ResponseEntity<String> trashUser(@PathVariable Long id) {
        userServiceImpl.trashUser(id);
        return ResponseEntity.ok("User moved to trash.");
    }

    @PutMapping("/{id}/restore")
    public ResponseEntity<String> restoreUser(@PathVariable Long id) {
        userServiceImpl.restoreUser(id);
        return ResponseEntity.ok("User restored from trash.");
    }
}
