package com.cognix.authservice.Dtos;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Login response containing authentication tokens and user information")
public class LoginResponse {
    
    @Schema(description = "JWT access token", example = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;
    
    @Schema(description = "Token type", example = "Bearer")
    private String tokenType = "Bearer";
    
    @Schema(description = "Token expiration time in milliseconds", example = "3600000")
    private Long expiresIn;
    
    @Schema(description = "User ID", example = "123")
    private Long userId;
    
    @Schema(description = "Username", example = "john_doe")
    private String username;
    
    @Schema(description = "User email", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "User roles", example = "[\"ROLE_USER\"]")
    private String[] roles;
    
    public LoginResponse(String accessToken, Long expiresIn, Long userId, String username, String email, String[] roles) {
        this.accessToken = accessToken;
        this.expiresIn = expiresIn;
        this.userId = userId;
        this.username = username;
        this.email = email;
        this.roles = roles;
    }
}
