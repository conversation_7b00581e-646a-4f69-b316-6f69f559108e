package com.cognix.authservice.Dtos;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "Password reset request with token verification")
public class PasswordResetDto {

    @Schema(description = "New password", example = "newSecurePassword123", required = true)
    String newPassword;

    @Schema(description = "Password reset token received via email", example = "abc123def456", required = true)
    String token;

    @Schema(description = "User email address", example = "<EMAIL>", required = true)
    String email;
}
