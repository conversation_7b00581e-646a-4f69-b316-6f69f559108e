package com.cognix.authservice.Dtos;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "Email verification request with token")
public class ValidateEmailDto {

    @Schema(description = "Email verification token received via email", example = "abc123def456", required = true)
    String token;

    @Schema(description = "User email address to verify", example = "<EMAIL>", required = true)
    String email;

}
