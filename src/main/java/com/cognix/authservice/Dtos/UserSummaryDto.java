package com.cognix.authservice.Dtos;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "User summary information for public display")
public class UserSummaryDto {
    
    @Schema(description = "User ID", example = "123")
    private Long id;
    
    @Schema(description = "Username", example = "john_doe")
    private String username;
    
    @Schema(description = "User email", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "First name", example = "John")
    private String firstName;
    
    @Schema(description = "Last name", example = "Doe")
    private String lastName;
    
    @Schema(description = "Email verification status", example = "true")
    private Boolean emailVerified;
    
    @Schema(description = "User status", example = "ACTIVE")
    private String status;
    
    @Schema(description = "User roles", example = "[\"ROLE_USER\"]")
    private String[] roles;
}
