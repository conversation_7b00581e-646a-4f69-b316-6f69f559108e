package com.cognix.authservice.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.cognix.authservice.model.UserEntity;

@Repository
public interface UserRepository extends JpaRepository<UserEntity, Long>{
    
    Optional<UserEntity> findByUsername(String username);

    Optional<UserEntity> findByEmail(String email);

    boolean existsByEmail(String email);

    // Fetch active users (not trashed)
    List<UserEntity> findByIsTrashedFalse();

    // Fetch trashed users
    List<UserEntity> findByIsTrashedTrue();
}
