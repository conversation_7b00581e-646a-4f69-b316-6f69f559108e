package com.cognix.authservice.security;

import java.security.Key;
import java.time.Instant;
import java.util.Date;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoderParameters;
import org.springframework.stereotype.Component;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
import lombok.RequiredArgsConstructor;

import com.cognix.authservice.model.UserEntity;
import com.nimbusds.jose.jwk.RSAKey;

@Component
@RequiredArgsConstructor
public class JwtEncoderImpl {

    @Value("${application.security.jwt.refresh-token.expiration}")
    private long refreshTokenExpiration;


    private final JwtEncoder jwtEncoder;

    private final RSAKey rsaKey;

     public String generateToken(UserEntity user, Authentication authentication, long expiryDuration){

        Instant now = Instant.now();
        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                    .issuer("beyond-self")
                    .issuedAt(now)
                    .expiresAt(now.plusSeconds(expiryDuration))
                    .subject(authentication.getName())
                    // .claim("role", authentication.getAuthorities().toString())
                    .claim("authorities", authentication.getAuthorities()
                        .stream()
                        .map(GrantedAuthority::getAuthority)
                        .collect(Collectors.toList()))
                    .claim("firstName", user.getFirstName())
                    .claim("lastName", user.getLastName())
                    .claim("gender", user.getGender())
                    .claim("dateOfBirth", user.getDateOfBirth())
                    .claim("phoneNumber", user.getPhoneNumber())
                    .claim("id", user.getId())

                    .build();

               return jwtEncoder.encode(JwtEncoderParameters.from(claimsSet)).getTokenValue();
    }


    public String generateRefreshToken(UserEntity user, Authentication authentication, long expiryDuration){

        Instant now = Instant.now();
        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                    .issuer("beyond-self")
                    .issuedAt(now)
                    .expiresAt(now.plusSeconds(refreshTokenExpiration))
                    .subject(authentication.getName())
                    .claim("authorities", authentication.getAuthorities()
                        .stream()
                        .map(GrantedAuthority::getAuthority)
                        .collect(Collectors.toList()))
                    .claim("firstName", user.getFirstName())
                    .claim("lastName", user.getLastName())
                    .claim("gender", user.getGender())
                    .claim("dateOfBirth", user.getDateOfBirth())
                    .claim("phoneNumber", user.getPhoneNumber())
                    .claim("id", user.getId())

                    .build();

               return jwtEncoder.encode(JwtEncoderParameters.from(claimsSet)).getTokenValue();
    }


    public String getUsernameFromToken(String token) throws SignatureException, ExpiredJwtException, UnsupportedJwtException, MalformedJwtException, IllegalArgumentException, Exception {
        return extractClaim(token, Claims::getSubject);
    }

    private <T> T extractClaim(String token, Function<Claims, T> claimResolver) throws SignatureException, ExpiredJwtException, UnsupportedJwtException, MalformedJwtException, IllegalArgumentException, Exception {
        final Claims claims = extractAllClaims(token);
        return claimResolver.apply(claims);
    }

    public Claims extractAllClaims(String token) throws SignatureException, ExpiredJwtException, UnsupportedJwtException, MalformedJwtException, IllegalArgumentException, Exception {
        return Jwts.parserBuilder()
                .setSigningKey(getKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    public boolean isTokenExpired(String token) throws SignatureException, ExpiredJwtException, UnsupportedJwtException, MalformedJwtException, IllegalArgumentException, Exception {
        return extractExpiration(token).before(new Date());
    }

    private Date extractExpiration(String token) throws SignatureException, ExpiredJwtException, UnsupportedJwtException, MalformedJwtException, IllegalArgumentException, Exception {
        return extractClaim(token, Claims::getExpiration);
    }

    private Key getKey() throws Exception {
        // Use the RSA public key from the injected RSAKey bean.
        return rsaKey.toRSAPublicKey();
    }
    
}
