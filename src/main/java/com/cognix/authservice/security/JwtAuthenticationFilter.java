package com.cognix.authservice.security;

import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.cognix.authservice.model.UserEntity;
import com.cognix.authservice.services.UserServiceImpl;

import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final @Lazy JwtEncoderImpl jwtEncoderImpl;
    private final UserServiceImpl userServiceImpl;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain)
            throws ServletException, IOException {

        String authHeader = request.getHeader("Authorization");

        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            try {
                Claims claims = jwtEncoderImpl.extractAllClaims(token);

                String username = claims.getSubject();

                // Extract the authorities claim as a List (make sure the claim name matches the one set during token generation)
                @SuppressWarnings("unchecked")
                List<String> authorityList = claims.get("authorities", List.class);
                List<GrantedAuthority> authorities = authorityList.stream()
                        .map(SimpleGrantedAuthority::new)
                        .collect(Collectors.toList());

                // Optionally, retrieve the full user details if needed
                UserEntity user = userServiceImpl.getUser(username);
                request.setAttribute("authenticatedUser", user);

                // Create an authentication object with the extracted authorities
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(username, null, authorities);

                // Set the authentication in the SecurityContext
                SecurityContextHolder.getContext().setAuthentication(authentication);

            } catch (Exception e) {
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid or expired token");
                return;
            }
        }

        filterChain.doFilter(request, response);
    }

    
}
