package com.cognix.authservice.services;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.cognix.authservice.email.EmailService;
import com.cognix.authservice.email.VerificationEncoder;
import com.cognix.authservice.model.RoleEntity;
import com.cognix.authservice.model.RoleName;
import com.cognix.authservice.model.UserEntity;
import com.cognix.authservice.repository.UserRepository;
import com.cognix.authservice.request.SignUpRequest;
import com.cognix.authservice.security.JwtEncoderImpl;
import com.fasterxml.jackson.core.exc.StreamWriteException;
import com.fasterxml.jackson.databind.DatabindException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
// import io.jsonwebtoken.ExpiredJwtException;
// import io.jsonwebtoken.MalformedJwtException;
// import io.jsonwebtoken.UnsupportedJwtException;
// import io.jsonwebtoken.security.SignatureException;
import jakarta.mail.MessagingException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService{

    private final JwtEncoderImpl jwtEncoderImpl;

    private final EmailService emailService;

    private final int durationInMinutes = 15;

    private final VerificationEncoder verificationEncoder;

    private final UserRepository userRepository;

    private final RoleService roleService;

    private final PasswordEncoder passwordEncoder;

    private final AuthenticationManager authenticationManager;


    @Override
    public Map<String, Object> registerUser(SignUpRequest signUpRequest) throws MessagingException, UnsupportedEncodingException{

        Map<String, Object> registrationResponse = new HashMap<>();
        
        Optional<UserEntity> userEntity = userRepository.findByUsername(signUpRequest.getUsername());

        if(userEntity.isPresent()){
            
            throw new IllegalArgumentException("Username already taken: " + signUpRequest.getUsername());
        }

        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            throw new IllegalArgumentException("Email already exists: " + signUpRequest.getEmail());
        }

        UserEntity newUser = new UserEntity();
        newUser.setUsername(signUpRequest.getUsername());
        newUser.setPassword(passwordEncoder.encode(signUpRequest.getPassword()));
        newUser.setEmail(signUpRequest.getEmail());
        newUser.setFirstName(signUpRequest.getFirstName());
        newUser.setLastName(signUpRequest.getLastName());
        newUser.setGender(signUpRequest.getGender());
        newUser.setDateOfBirth(signUpRequest.getDateOfBirth());
        newUser.setPhoneNumber(signUpRequest.getPhoneNumber());
        

        
        
       // Assign role, default to ROLE_USER if no role is specified
       RoleEntity role = (signUpRequest.getRoleName() == null || signUpRequest.getRoleName().isEmpty()) 
            ? roleService.findByName(RoleName.ROLE_USER) 
            : roleService.findByName(RoleName.valueOf("ROLE_" + signUpRequest.getRoleName().toUpperCase()));
        
        newUser.setRole(role);


        String emailVerificationToken = generateEmailVerificationToken();
        String hashedToken = verificationEncoder.encode(emailVerificationToken);
        newUser.setEmailVerificationToken(hashedToken);
        newUser.setEmailVerificationTokenExpiryDate(LocalDateTime.now().plusMinutes(durationInMinutes));

        userRepository.save(newUser);

        String subject = "Email Verification";
        String body = String.format("""
                        Only one step to complete registration.
                        
                        Enter this code to verify your email: %s. The code will expire in %s minutes.""",
                emailVerificationToken, durationInMinutes);
        try {
            emailService.sendEmail(signUpRequest.getEmail(), subject, body);
        } catch (Exception e) {
            log.info("Error while sending email: {}", e.getMessage());
        }


        Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(signUpRequest.getUsername(), signUpRequest.getPassword()));



        String authToken = jwtEncoderImpl.generateToken(newUser, authentication, 3600 );

        registrationResponse.put("auth_token", authToken);
        registrationResponse.put("expires_in", 900);
        
        return registrationResponse;
    }


    public Map<String, Object> login(String username, String password){
        Optional<UserEntity> userEntity = userRepository.findByUsername(username);

        Map<String, Object> response = new HashMap<>();

        Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(username, password));
        if(!userEntity.isPresent()){
            response.put("status", "User Not Found");
            return response;
        }

        if(!userEntity.get().getEmailVerified()){
            response.put("verification_status", "Please verify your email to proceed");
            return response;
        }

        if(userEntity.get().isTrashed()){
            response.put("Account Status", "Account is disabled, please reach out to the Admin.");
            return response;
        }


        String accessToken = jwtEncoderImpl.generateToken(userEntity.get(), authentication, 3600 );

        String refreshToken = jwtEncoderImpl.generateRefreshToken(userEntity.get(), authentication, durationInMinutes);

        log.info("Login successful");
        response.put("access_token", accessToken);
        response.put("expires_in", 3600);
        response.put("refresh_token",refreshToken);

        return response;
    }


    public static String generateEmailVerificationToken() {
        SecureRandom random = new SecureRandom();
        StringBuilder token = new StringBuilder(5);
        for (int i = 0; i < 5; i++) {
            token.append(random.nextInt(10));
        }
        return token.toString();
    }

     public void sendEmailVerificationToken(String username){

        // String username = jwtEncoderImpl.getUsernameFromToken(token);

        log.info("This here is the username..."+ username);
        Optional<UserEntity> user = userRepository.findByUsername(username);

        log.info(user.isPresent()? "Yes": "No");
    

        if (user.isPresent() && !user.get().getEmailVerified()) {
            String emailVerificationToken = generateEmailVerificationToken();
            String hashedToken = verificationEncoder.encode(emailVerificationToken);
            user.get().setEmailVerificationToken(hashedToken);
            user.get().setEmailVerificationTokenExpiryDate(LocalDateTime.now().plusMinutes(durationInMinutes));
            userRepository.save(user.get());
            String subject = "Email Verification";
            String body = String.format("Only one step remaining to complete registration.\n\n"
                            + "Enter this code to verify your email: " + "%s\n\n" + "The code will expire in " + "%s"
                            + " minutes.",
                    emailVerificationToken, durationInMinutes);
            try {
                emailService.sendEmail(user.get().getEmail(), subject, body);
            } catch (Exception e) {
                log.info("Error while sending email: {}", e.getMessage());
            }
        } else {
            throw new IllegalArgumentException("Email verification token failed, or email is already verified.");
        }
    }

    public void validateEmailVerificationToken(String token, String email) {
        Optional<UserEntity> user = userRepository.findByEmail(email);
        if (user.isPresent() && verificationEncoder.matches(token, user.get().getEmailVerificationToken())
                && !user.get().getEmailVerificationTokenExpiryDate().isBefore(LocalDateTime.now())) {
            user.get().setEmailVerified(true);
            user.get().setEmailVerificationToken(null);
            user.get().setEmailVerificationTokenExpiryDate(null);
            userRepository.save(user.get());
        } else if (user.isPresent() && verificationEncoder.matches(token, user.get().getEmailVerificationToken())
                && user.get().getEmailVerificationTokenExpiryDate().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("Email verification token expired.");
        } else {
            throw new IllegalArgumentException("Email verification token failed.");
        }
    }


    public void sendPasswordResetToken(String email) {

       
        Optional<UserEntity> user = userRepository.findByEmail(email);
        if (user.isPresent()) {
            String passwordResetToken = generateEmailVerificationToken();
            String hashedToken = verificationEncoder.encode(passwordResetToken);
            user.get().setPasswordResetToken(hashedToken);
            user.get().setPasswordResetTokenExpiryDate(LocalDateTime.now().plusMinutes(durationInMinutes));
            userRepository.save(user.get());
            String subject = "Password Reset";
            String body = String.format("""
                            You requested a password reset.
                            
                            Enter this code to reset your password: %s. The code will expire in %s minutes.""",
                    passwordResetToken, durationInMinutes);
            try {
                emailService.sendEmail(email, subject, body);
            } catch (Exception e) {
                log.info("Error while sending email: {}", e.getMessage());
            }
        } else {
            throw new IllegalArgumentException("User not found.");
        }
    }


    public void resetPassword(String email, String newPassword, String token) {
        Optional<UserEntity> user = userRepository.findByEmail(email);
        if (user.isPresent() && verificationEncoder.matches(token, user.get().getPasswordResetToken())
                && !user.get().getPasswordResetTokenExpiryDate().isBefore(LocalDateTime.now())) {
            user.get().setPasswordResetToken(null);
            user.get().setPasswordResetTokenExpiryDate(null);
            user.get().setPassword(passwordEncoder.encode(newPassword));
            userRepository.save(user.get());
        } else if (user.isPresent() && verificationEncoder.matches(token, user.get().getPasswordResetToken())
                && user.get().getPasswordResetTokenExpiryDate().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("Password reset token expired.");
        } else {
            throw new IllegalArgumentException("Password reset token failed.");
        }
    }

    public UserEntity getUser(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("User not found."));
    }


    public List<UserEntity> getAllActiveUsers() {
        return userRepository.findByIsTrashedFalse();
    }

    public List<UserEntity> getTrashedUsers() {
        return userRepository.findByIsTrashedTrue();
    }

    public void trashUser(Long userId) {
        UserEntity user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        user.setTrashed(true);
        userRepository.save(user);
    }

    public void restoreUser(Long userId) {
        UserEntity user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        user.setTrashed(false);
        userRepository.save(user);
    }


    public void refreshToken(HttpServletRequest request, HttpServletResponse response) throws SignatureException, ExpiredJwtException, StreamWriteException, DatabindException, UnsupportedJwtException, MalformedJwtException, IllegalArgumentException, AuthenticationException, IOException, Exception {
       
    final String authHeader = request.getHeader("Authorization");
    if (authHeader == null || !authHeader.startsWith("Bearer ")) {
        response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Refresh token is missing");
        return;
    }
    
    final String refreshToken = authHeader.substring(7);
    final String username = jwtEncoderImpl.getUsernameFromToken(refreshToken);
    
    if (username != null) {
        var user = this.userRepository.findByUsername(username)
                          .orElseThrow(() -> new UsernameNotFoundException("User not found"));
        
        if (!jwtEncoderImpl.isTokenExpired(refreshToken)) {
            
            GrantedAuthority authority = new SimpleGrantedAuthority(user.getRole().getName().name());
            Authentication authentication = new UsernamePasswordAuthenticationToken(
                user,
                null,
                Collections.singletonList(authority)
            );

            var accessToken = jwtEncoderImpl.generateToken(user, authentication, 3600);
            Map<String, Object> authResponse = new HashMap<>();
            authResponse.put("access_token", accessToken);
            authResponse.put("refresh_token", refreshToken);
    
            response.setContentType("application/json");
            new ObjectMapper().writeValue(response.getOutputStream(), authResponse);
        } else {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Refresh token is expired");
        }
    } else {
        response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid refresh token");
    }
}

    public void updateProfilePicture(Long userId, MultipartFile file) throws IOException {
        UserEntity user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        user.setProfilePicture(file.getBytes());
        userRepository.save(user);
    }

    public byte[] getProfilePicture(Long userId) {
        UserEntity user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        return user.getProfilePicture();
    }


    public boolean deleteProfilePicture(Long id) {
       
        Optional<UserEntity> optionalUser = userRepository.findById(id);
    if (optionalUser.isPresent()) {
        UserEntity user = optionalUser.get();
        
        user.setProfilePicture(null);
        userRepository.save(user);
        
        return true;
    }
    return false;
    }

}
