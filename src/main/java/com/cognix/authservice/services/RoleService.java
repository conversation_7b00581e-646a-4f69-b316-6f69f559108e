package com.cognix.authservice.services;

import org.springframework.stereotype.Service;

import com.cognix.authservice.model.RoleEntity;
import com.cognix.authservice.model.RoleName;
import com.cognix.authservice.repository.RoleRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class RoleService {
    
    private final RoleRepository roleRepository;

    public RoleEntity findByName(RoleName roleName) {
        return roleRepository.findByName(roleName)
                .orElseThrow(() -> new RuntimeException("Role not found: " + roleName));
    }

    public void initializeRoles() {
        if (roleRepository.count() == 0) {
            roleRepository.save(new RoleEntity(null, RoleName.ROLE_USER));
            roleRepository.save(new RoleEntity(null, RoleName.ROLE_ADMIN));
            roleRepository.save(new RoleEntity(null, RoleName.ROLE_THERAPIST));
        }
    }
}
