package com.cognix.authservice.seeds;


import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import com.cognix.authservice.services.RoleService;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class RoleSeeder implements CommandLineRunner {
    
    private final RoleService roleService;

    @Override
    public void run(String... args) throws Exception {
        roleService.initializeRoles(); // Populate roles on application startup
    }
}
