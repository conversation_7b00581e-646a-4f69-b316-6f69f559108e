package com.cognix.authservice.email;


import java.security.MessageDigest;

import java.util.Base64;

import java.security.NoSuchAlgorithmException;

import org.springframework.stereotype.Component;

@Component
public class VerificationEncoder {

     public String encode(CharSequence rawPassword) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(rawPassword.toString().getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Error encoding password", e);
        }
    }

    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        return encode(rawPassword).equals(encodedPassword);
    }
    
}
